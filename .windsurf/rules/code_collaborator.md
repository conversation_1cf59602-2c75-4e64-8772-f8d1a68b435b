# Human + AI Code Collaboration Guide

We build Continuia Health OS with **humans + AI as co-developers**.

## Collaboration Rules

### 1. AI’s Role
- Draft **scaffolds**: SQL schemas, Temporal stubs, Storybook stories.
- Generate **repetitive code**: CRUD endpoints, Zod schemas, OpenAPI docs.
- Suggest **patterns**, never silently change business logic.

### 2. Human’s Role
- Set **architecture direction** and guardrails.
- Review AI outputs — **no blind commits**.
- Focus on domain logic, compliance, and clinician empathy.

### 3. Golden Rules
- **Never** let AI bypass compliance guardrails (PHI, auth, provenance).
- **Never** let AI commit migrations or secrets directly.
- **Always** run generated code through CI + schema validators.

### 4. Workflow
- Humans describe intent in **plain language + manifestos**.
- AI generates **draft PRs / patches**.
- Humans review → accept → CI validates.
- Repeat.

### 5. Safety Nets
- All schemas validated against Git-locked Zod/JSON schemas.
- Storybook visual diffs catch UI regressions.
- Temporal workflows tested with fake workers before prod.

### 6. Style Consistency
- AI outputs must follow **Prettier + ESLint + lint-staged**.
- Commit messages: `feat:`, `fix:`, `chore:` style.

### 7. Feedback Loop
- If AI generates off-pattern code → add/update a **manifesto rule**.
- Manifestos live in repo (`prj_manifesto.md`, `vibe_coding_manifesto.md`, this file).

---

## Why?
> Humans design intent, AI accelerates execution.  
> The system is only as safe as the rules we enforce together.
