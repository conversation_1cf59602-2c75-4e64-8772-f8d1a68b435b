---
trigger: always_on
---

# Technical Architecture Specification

**Purpose**: Authoritative technical architecture for Continuia Health OS development.
**Audience**: Developers implementing the system.
**Status**: Final - no further architectural debate.

---

## 1. Core Principles

1. **Agentic-first**  
   - Temporal workflows for orchestration.  
   - AI agents interact at the same layer as humans (UI + APIs).  

2. **UI-as-Config**  
   - Storybook components + prop schemas live in Git.  
   - Screens/layouts live in DB, editable in production, export/import supported.  

3. **Data-first**  
   - Canonical store in Postgres JSONB.  
   - Projections (materialized views/tables) for fast reads.  
   - Outbox pattern + NATS for async events.  

4. **Blue/Green Evolution**  
   - Packs for UI screens.  
   - Shadow projections + search_path swaps for DB.  
   - Zero-downtime rollouts.  

5. **Compliance by Default**  
   - P<PERSON><PERSON> never leaves DB → no logs, NATS, or Temporal history.  
   - Provenance stamped automatically.  
   - Jurisdiction packs handle regional rules.  

6. **Low Cognitive Load for Devs**  
   - Developers only think in **three nouns**:  
     - `Collection` (data bucket)  
     - `Screen` (UI layout)  
     - `Workflow` (process)  

---

## 2. Data Model (Postgres)

### 2.1 Canonical Records
```sql
records(
  id uuid primary key,
  tenant_id uuid not null,
  collection_id uuid not null,
  version int not null,
  body jsonb not null,
  created_at timestamptz,
  updated_at timestamptz
);
```

### 2.2 Projections
- One table per hot view, fully indexed.
- Always keyset pagination (never OFFSET).
- Example:
```sql
proj_tasks_grid(
  tenant_id uuid,
  record_id uuid primary key,
  title text,
  status text,
  assignee_display text,
  due_date date,
  updated_at timestamptz
);
```

### 2.3 Outbox + Provenance
```sql
outbox(id bigserial pk, tenant_id uuid, collection_id uuid,
       record_id uuid, event_type text, occurred_at timestamptz, published bool);
provenance(id bigserial pk, tenant_id uuid, record_id uuid,
           actor jsonb, action text, at timestamptz);
```

---

## 3. Event Fabric

- **NATS JetStream** is the event bus.  
- Outbox relayer publishes `data.<collection>.(created|updated|deleted)`.  
- Projection services consume → upsert into projections.  
- UI gets live patches via WS bridge (optional).  

---

## 4. Workflows (Temporal)

- One workflow per record (e.g. `EncounterWorkflow`, `TaskWorkflow`).  
- Bound via `wf_binding` table:
```sql
wf_binding(tenant_id, collection_slug, record_id, wf_type, wf_id, run_id, status, updated_at);
```

- UI/agents never call Temporal SDKs directly.  
- REST façade:
```
POST /wf/start   { workflowType, recordId }
POST /wf/signal  { workflowType, recordId, signal, args }
POST /wf/query   { workflowType, recordId, query, args }
GET  /wf/status?workflowType&recordId
```

- **PHI never in workflow inputs/history**. Only IDs.  
- Activities fetch PHI from DB via APIs.

---

## 5. UI Architecture

### 5.1 Components (in Git)
- `@ui` — React + Tailwind + shadcn.  
- All components Storybook-tested.  
- Prop schemas validated in Zod/AJV.  

### 5.2 Renderer (in Git)
- `@ui-runtime` maps JSON → React tree.  
- Supports data bindings (record, projection, workflow query).  
- Supports actions (write, workflow signal, refresh).  

### 5.3 Screens (in DB)
```sql
ui_screen(id uuid pk, tenant_id uuid, collection_slug, view_slug, type, published_version int);
ui_screen_version(id bigserial pk, tenant_id, screen_id, version int, status text, definition jsonb);
```

- Draft → staged → active via **packs**:
```sql
ui_screen_pack(id uuid pk, tenant_id, name, status, traffic_percent int);
ui_screen_pack_item(pack_id, screen_id, screen_version);
```

- Export/import bundles supported.

### 5.4 URL Contract
```
/ui/<collection>/<view>/<type>?id=<recordId>&pack=<packId>&act=<actionKey>
```

---

## 6. Block Layouts

- A **screen** can be a **layout of blocks**.  
- **Block** = self-contained view (table, form, board, calendar, timeline, card grid, inbox, etc.).  
- Blocks emit events (`selection.changed`, `row.opened`, `dateRange.changed`).  
- Links declaratively wire block events to other block inputs.

**Example link config:**
```json
{
  "from": { "block":"tasksTable","event":"selection.changed" },
  "to": [
    { "block":"taskDetail","set":{"data.idFrom":"$event.record_id"} },
    { "block":"timeline","mergeFilters":[{"key":"record_id","op":"in","value":"$event.selection"}] }
  ]
}
```

- Supported link ops: `set`, `mergeFilters`, `signal`, `refresh`.

---

## 7. Notifications (Human-in-the-Loop)

- Workflow creates task → Notification activity sends deep link.  
- Deep link format:
```
/ui/<collection>/<view>/<type>?id=<recordId>&act=<actionKey>&sig=<JWS>
```

- JWS short-lived, single-use, no PHI.  
- In-app inbox block + table for actionable tasks.

---

## 8. APIs

### 8.1 Data
```
POST /data/:collection
PUT  /data/:collection/:id
GET  /data/:collection/:id
GET  /view/:projectionId?cursor=...
POST /search
```

### 8.2 Screens
```
GET  /ui/screen/:collection/:view/:type
POST /ui/screen/:id/version
POST /ui/pack
POST /ui/export /ui/import
```

### 8.3 Workflows
*(see section 4)*

---

## 9. Developer Guardrails

- **No custom SQL, NATS, or Temporal calls** in feature code.  
- Always use generated REST + hooks (`useProjection`, `useRecord`, `useWorkflowSignal`).  
- PHI never leaves DB (IDs only in events/workflows).  
- Always keyset pagination.  
- All changes logged with provenance.  

---

## 10. Project Vibe & Collaboration

- **Project Manifesto**: vision + north stars.  
- **Vibe Coding Manifesto**: feel → snappy, safe, simple.  
- **AI Collaboration Guide**: humans design intent, AI generates scaffolds, humans review.  
- All three docs live in repo root.  

---

## 11. Timeline (6-week MVP)

- **Week 1–2**: Base schema, NATS outbox, projection service, Temporal façade.  
- **Week 3**: Schema Studio + Write API + provenance.  
- **Week 4**: Screen Builder + pack router + blue/green projection swap.  
- **Week 5**: Workflow signals in screens + HITL notifications.  
- **Week 6**: Export/import bundles + Storybook regression tests.  

---

## 12. Compliance Defaults

- Row-level security by tenant.  
- Masking for PHI in projections.  
- Provenance + pgaudit enabled.  
- OIDC auth; ABAC via OPA middleware.  
- mTLS internal.  
- Blue/green rollouts for jurisdiction packs.  

---

# Golden Rule
**Devs think only in Collections, Screens, Workflows.  
Everything else is generated, declarative, or governed.**