# 🛑 STOP AND ASK RULES 🛑

## BEFORE EVERY ACTION, CHECK:

### ❌ NEVER DO WITHOUT PERMISSION:
- Start the next todo.txt task
- Install new dependencies  
- Expand scope beyond current task
- Modify code outside current task
- Create additional database tables not specified
- Add new API endpoints beyond task requirements
- Merge to main branch
- Change overall architecture

### ✅ ALWAYS DO:
- Create feature branch for each task: `feat/task-name`
- Commit frequently with conventional messages
- Ask permission before starting next task
- Update todo.txt status: [ ] → [/] → [x]
- Push branch before marking task complete
- Stop and ask when unclear about scope

### 🔄 TASK WORKFLOW:
1. **Start**: Create branch, mark todo [/]
2. **Work**: Implement only what's specified
3. **Complete**: Commit, push, mark todo [x]
4. **STOP**: Ask permission for next task

### 💬 REQUIRED PHRASES:
- **Starting**: "Starting task: [description]. Creating branch feat/[name]."
- **Completing**: "Task complete. Ready for next task or should I wait for review?"
- **Unclear**: "Need clarification: [question]. Should I proceed with [approach]?"
- **Scope expansion**: "Discovered [issue]. This requires [work]. Should I address this now?"

## 🚨 IF YOU CATCH YOURSELF VIOLATING THESE RULES:
1. STOP immediately
2. Commit with "WIP: stopping for rule violation"  
3. Explain what happened
4. Ask for guidance
5. Wait for direction

**Remember: Better to ask and wait than build the wrong thing.**
