# Continuia Health OS — Project Manifesto

## Why We Exist
Continuia Health OS is **not just another EHR**. It is a **Health Operating System**:
- Agentic-first (Temporal workflows + AI assistants as first-class citizens)
- UI-as-Config (Storybook in Git, screen JSON in DB)
- Compliant by default (HIPAA, DPDP, DHA)
- Built for **global hospitals** and clinicians, without friction

## North Stars
1. **Zero Friction for Clinicians**  
   - Snap-fast UI, familiar flows, no forced linearity  
   - AI augments, never replaces  

2. **Zero Surprises for Developers**  
   - Everything lives in **three nouns**: `Collection`, `Screen`, `Workflow`  
   - Declarative by default, generated where possible  
   - Inline first, presets/versioning only when needed  

3. **Compliance Is Built-In, Not Bolted On**  
   - PHI never leaks to logs/events  
   - Provenance is automatic  
   - Jurisdictional packs handle policy differences  

4. **Scalable & Evolvable**  
   - Schema-flexible (JSONB with projections)  
   - Stateless UI; workflows orchestrate state  
   - Blue/green rollouts with pack governance  

5. **Agentic Future-Ready**  
   - Humans and AI agents both interact at the UI layer  
   - Agents follow the same APIs and governance as humans  
   - “Health OS” foundation, not an app  

## Golden Rule
> **We optimize for lowest developer cognition and highest clinician trust.**
