# Continuia Vibe Coding Manifesto

We code for **vibe** — snappy, safe, self-explanatory.

## Principles

1. **Simple Mental Model**
   - Only think in Collections, Screens, Workflows.
   - Never wire raw NATS, Temporal, or SQL in feature code.

2. **Stateless & Declarative**
   - UI: described in JSON, rendered from Storybook-tested components.
   - Data: canonical JSONB rows + projections (for fast reads).
   - Workflows: declarative steps + signals; no hidden state machines.

3. **Blue/Green Everything**
   - Screens, projections, workflows evolve without downtime.
   - Old sessions continue; new sessions get new packs.

4. **Snappy by Default**
   - Target: <120ms p95 reads, <250ms writes, <2s projector lag.
   - Always keyset pagination, never OFFSET.

5. **Safe by Design**
   - No PHI in payloads, logs, or URLs.
   - All changes have provenance.
   - AuthZ checked on every read/write.

6. **Composable, Not Custom**
   - New screens = JSON + existing blocks.
   - New blocks = Storybook components + prop schemas.
   - Reuse before invention.

7. **Fail Fast in Dev, Never in Prod**
   - Draft → staged → active packs.
   - Visual regression via Storybook snapshots.
   - Outbox/parity checks before flipping search_path.

## The Feel
- Small diffs, fast CI, instant preview.
- No “god services” — each concern (UI, data, workflows) has its layer.
- Developers feel in control, clinicians feel confident.
