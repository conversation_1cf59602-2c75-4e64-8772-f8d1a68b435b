# Coding Standards & Principles

**Purpose**: Define code quality, architecture patterns, and development principles.  
**Audience**: Developers implementing Continuia Health OS.  
**Scope**: Technical implementation guidelines and constraints.

---

## Core Mental Model

### The Three Nouns (Only Think In These):
1. **Collection** - Data bucket (like a table, but JSONB-flexible)
2. **Screen** - UI layout (JSON definition rendered to React)
3. **Workflow** - Process orchestration (Temporal workflows)

**Everything else is generated, declarative, or governed.**

---

## Architecture Principles

### 1. Simple Mental Model
- Only think in Collections, Screens, Workflows
- Never wire raw NATS, Temporal, or SQL in feature code
- Use generated REST APIs and hooks (`useProjection`, `useRecord`, `useWorkflowSignal`)

### 2. Stateless & Declarative
- **UI**: Described in JSON, rendered from Storybook-tested components
- **Data**: Canonical JSONB rows + projections for fast reads
- **Workflows**: Declarative steps + signals, no hidden state machines

### 3. Performance Targets
- **Reads**: <120ms p95 response time
- **Writes**: <250ms p95 response time
- **Projector lag**: <2s from event to projection update
- **Always keyset pagination**, never OFFSET

### 4. Safety by Design
- **No PHI** in payloads, logs, URLs, or workflow history
- **All changes** have automatic provenance tracking
- **Authorization** checked on every read/write
- **Row-level security** by tenant_id

### 5. Blue/Green Everything
- Screens evolve without downtime via packs
- Projections swap via search_path changes
- Old sessions continue, new sessions get new versions

---

## Code Quality Standards

### TypeScript/JavaScript
```typescript
// ✅ Good: Use generated hooks
const { data, loading } = useProjection('tasks-grid', { cursor });
const { mutate } = useRecord('tasks');

// ❌ Bad: Direct database/API calls
const result = await db.query('SELECT * FROM tasks');
```

### Database Patterns
```sql
-- ✅ Good: Keyset pagination
SELECT * FROM proj_tasks 
WHERE updated_at > $cursor 
ORDER BY updated_at 
LIMIT 20;

-- ❌ Bad: Offset pagination
SELECT * FROM proj_tasks 
OFFSET 100 LIMIT 20;
```

### Error Handling
```typescript
// ✅ Good: Structured error handling
try {
  const result = await api.createRecord(collection, data);
  return { success: true, data: result };
} catch (error) {
  logger.error('Record creation failed', { 
    collection, 
    error: error.message,
    // Never log PHI data
  });
  return { success: false, error: error.message };
}
```

### Component Structure
```typescript
// ✅ Good: Prop schema validation
const TaskCardProps = z.object({
  taskId: z.string().uuid(),
  showDetails: z.boolean().default(false),
  onSelect: z.function().optional(),
});

export const TaskCard = ({ taskId, showDetails, onSelect }: z.infer<typeof TaskCardProps>) => {
  const { data: task } = useRecord('tasks', taskId);
  // Component implementation
};
```

---

## Security & Compliance Rules

### PHI Protection (Critical)
```typescript
// ✅ Good: Only IDs in events/workflows
await publishEvent('task.created', { 
  taskId: task.id,
  collectionId: 'tasks',
  tenantId: task.tenant_id 
});

// ❌ Bad: PHI in events
await publishEvent('task.created', { 
  patientName: task.patient_name, // NEVER!
  diagnosis: task.diagnosis        // NEVER!
});
```

### Provenance Tracking
```typescript
// ✅ Good: Automatic provenance (handled by framework)
const task = await createRecord('tasks', {
  title: 'Follow up appointment',
  status: 'pending'
}, { 
  actor: { userId: currentUser.id, role: 'nurse' },
  action: 'create'
});
```

### Authorization Patterns
```typescript
// ✅ Good: Use middleware for auth
app.use('/api/data', authMiddleware, tenantMiddleware);

// ❌ Bad: Manual auth checks everywhere
if (!user.canAccess(tenantId)) {
  throw new Error('Unauthorized');
}
```

---

## Testing Standards

### Unit Tests
```typescript
// ✅ Good: Test business logic, not implementation
describe('TaskWorkflow', () => {
  it('should create follow-up task when encounter is completed', async () => {
    const workflow = new TaskWorkflow();
    await workflow.completeEncounter({ encounterId: 'enc-123' });
    
    expect(mockCreateRecord).toHaveBeenCalledWith('tasks', {
      type: 'follow-up',
      parentId: 'enc-123'
    });
  });
});
```

### Component Tests (Storybook)
```typescript
// ✅ Good: Visual regression testing
export const Default: Story = {
  args: {
    taskId: 'task-123',
    showDetails: false
  }
};

export const WithDetails: Story = {
  args: {
    taskId: 'task-123', 
    showDetails: true
  }
};
```

---

## File Organization

### Project Structure
```
packages/
├── @ui/                    # React components + Storybook
├── @ui-runtime/           # JSON → React renderer
├── @api/                  # REST APIs + validation
├── @workflows/            # Temporal workflows
├── @projections/          # Event consumers
└── @shared/               # Common types + utilities
```

### Naming Conventions
- **Files**: kebab-case (`task-card.tsx`, `user-service.ts`)
- **Components**: PascalCase (`TaskCard`, `UserProfile`)
- **Functions**: camelCase (`createTask`, `getUserById`)
- **Constants**: SCREAMING_SNAKE_CASE (`MAX_RETRY_COUNT`)
- **Database**: snake_case (`task_id`, `created_at`)

---

## Performance Guidelines

### Database Queries
- Always use prepared statements
- Include tenant_id in WHERE clauses for RLS
- Use appropriate indexes for query patterns
- Prefer projections over complex JOINs

### API Design
- Use cursor-based pagination
- Include ETag headers for caching
- Implement request timeouts
- Use connection pooling

### Frontend Performance
- Lazy load components and routes
- Use React.memo for expensive components
- Implement virtual scrolling for large lists
- Optimize bundle size with tree shaking

---

## Forbidden Patterns

### ❌ Never Do These:
```typescript
// Direct database access in components
const tasks = await db.query('SELECT * FROM tasks');

// PHI in logs or events
console.log('Patient data:', patient);

// Offset pagination
const page = offset / limit;

// Hardcoded tenant IDs
WHERE tenant_id = 'tenant-123'

// Synchronous workflow activities
const result = syncApiCall(); // Use async activities only

// Custom SQL in feature code
const customQuery = 'SELECT custom_field FROM...';
```

### ✅ Use These Instead:
```typescript
// Generated hooks and APIs
const { data: tasks } = useProjection('tasks-grid');

// Structured logging without PHI
logger.info('Task created', { taskId, tenantId });

// Keyset pagination
const { data, nextCursor } = useProjection('tasks', { cursor });

// Dynamic tenant context
const { tenantId } = useTenant();

// Temporal activities
const result = await ctx.run(apiActivity, params);

// Generated projections
const projection = generateProjection(collectionSchema);
```

---

## Code Review Checklist

Before submitting code:
- [ ] Follows three-noun mental model
- [ ] No PHI in logs, events, or URLs
- [ ] Uses keyset pagination
- [ ] Includes proper error handling
- [ ] Has TypeScript types and validation
- [ ] Includes relevant tests
- [ ] Follows naming conventions
- [ ] No hardcoded values
- [ ] Proper tenant isolation
- [ ] Performance considerations addressed
