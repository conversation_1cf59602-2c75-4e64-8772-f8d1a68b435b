# AI-Human Collaboration Rules

**Purpose**: Define safe collaboration between human architects and AI developers.  
**Audience**: AI agents working on Continuia Health OS.  
**Critical**: Ensures AI stays within bounds while maximizing productivity.

---

## Collaboration Model

### Human Responsibilities:
- **Architecture decisions** and system design
- **Business logic** and domain rules
- **Security requirements** and compliance rules
- **Code review** and final approval
- **Task definition** and scope setting

### AI Responsibilities:
- **Code scaffolding** and boilerplate generation
- **Repetitive implementation** (CRUD, schemas, tests)
- **Pattern following** based on established examples
- **Documentation generation** from code
- **Refactoring** within defined constraints

---

## AI Operational Rules

### ✅ AI CAN Do Autonomously:
- Generate SQL schemas from specifications
- Create REST API endpoints following patterns
- Write Zod validation schemas
- Generate TypeScript types from schemas
- Create Storybook stories for components
- Write unit tests for business logic
- Generate OpenAPI documentation
- Create database migrations
- Implement CRUD operations
- Follow established coding patterns

### ❌ AI CANNOT Do Without Permission:
- Change core architecture decisions
- Modify security or compliance rules
- Install new dependencies
- Change database schema structure
- Modify authentication/authorization logic
- Create new API patterns
- Change workflow definitions
- Modify tenant isolation rules
- Alter performance requirements
- Change the three-noun mental model

---

## Safety Guardrails

### Compliance Boundaries (Never Cross):
```typescript
// ❌ NEVER: PHI in logs, events, or workflow history
logger.info('Patient diagnosis', { diagnosis: patient.diagnosis });
await workflow.start({ patientData: patient });

// ✅ ALWAYS: Only IDs and metadata
logger.info('Record accessed', { recordId: patient.id, tenantId });
await workflow.start({ recordId: patient.id });
```

### Security Boundaries (Never Cross):
```typescript
// ❌ NEVER: Bypass tenant isolation
SELECT * FROM records WHERE id = $1; // Missing tenant_id check

// ✅ ALWAYS: Include tenant context
SELECT * FROM records WHERE id = $1 AND tenant_id = $2;
```

### Architecture Boundaries (Never Cross):
```typescript
// ❌ NEVER: Direct database calls in components
const data = await db.query('SELECT * FROM tasks');

// ✅ ALWAYS: Use generated hooks
const { data } = useProjection('tasks-grid');
```

---

## Code Generation Guidelines

### When Generating Schemas:
```sql
-- ✅ Good: Follow canonical pattern
CREATE TABLE records (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id uuid NOT NULL,
  collection_id uuid NOT NULL,
  version int NOT NULL DEFAULT 1,
  body jsonb NOT NULL,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Include RLS policy
ALTER TABLE records ENABLE ROW LEVEL SECURITY;
CREATE POLICY tenant_isolation ON records 
  FOR ALL TO authenticated 
  USING (tenant_id = current_setting('app.tenant_id')::uuid);
```

### When Generating APIs:
```typescript
// ✅ Good: Follow REST pattern with validation
export const createRecord = async (req: Request, res: Response) => {
  const { collection } = req.params;
  const schema = getCollectionSchema(collection);
  
  try {
    const validatedData = schema.parse(req.body);
    const record = await recordService.create(
      collection,
      validatedData,
      { tenantId: req.tenantId, userId: req.userId }
    );
    
    res.status(201).json({ success: true, data: record });
  } catch (error) {
    logger.error('Record creation failed', { 
      collection, 
      tenantId: req.tenantId,
      error: error.message 
    });
    res.status(400).json({ success: false, error: error.message });
  }
};
```

### When Generating Components:
```typescript
// ✅ Good: Follow component pattern
const TaskCardProps = z.object({
  taskId: z.string().uuid(),
  variant: z.enum(['compact', 'detailed']).default('compact'),
  onAction: z.function().optional(),
});

export const TaskCard = ({ taskId, variant, onAction }: z.infer<typeof TaskCardProps>) => {
  const { data: task, loading, error } = useRecord('tasks', taskId);
  
  if (loading) return <Skeleton className="h-24 w-full" />;
  if (error) return <ErrorCard message="Failed to load task" />;
  if (!task) return <EmptyCard message="Task not found" />;
  
  return (
    <Card className={cn("task-card", variant === 'compact' && "h-24")}>
      {/* Component implementation */}
    </Card>
  );
};
```

---

## Quality Assurance

### Before Submitting Generated Code:
- [ ] Follows established patterns exactly
- [ ] Includes proper TypeScript types
- [ ] Has Zod validation where needed
- [ ] Includes error handling
- [ ] Follows naming conventions
- [ ] Has tenant isolation
- [ ] No PHI in logs or events
- [ ] Uses keyset pagination
- [ ] Includes basic tests
- [ ] Has proper documentation

### Testing Generated Code:
```typescript
// ✅ Good: Test business logic, not implementation
describe('TaskService', () => {
  it('should create task with proper tenant isolation', async () => {
    const task = await taskService.create(
      { title: 'Test task', status: 'pending' },
      { tenantId: 'tenant-123', userId: 'user-456' }
    );
    
    expect(task.tenant_id).toBe('tenant-123');
    expect(task.created_by).toBe('user-456');
  });
});
```

---

## Communication Patterns

### When Starting Code Generation:
"Generating [component/API/schema] following established pattern from [reference]. Will include [specific features]."

### When Encountering Ambiguity:
"Found ambiguity in [specific area]. Current pattern suggests [approach A], but requirement implies [approach B]. Please clarify which approach to use."

### When Hitting Boundaries:
"This task requires [specific action] which crosses [boundary type]. Need permission to proceed or alternative approach."

### When Completing Generation:
"Generated [component] following [pattern]. Includes [features]. Ready for review. Should I proceed with [next logical step] or wait for feedback?"

---

## Pattern Learning

### When Establishing New Patterns:
1. **Human provides example** with clear explanation
2. **AI confirms understanding** by restating pattern
3. **AI implements first instance** with human review
4. **Pattern gets documented** in coding standards
5. **AI follows pattern** in future implementations

### When Patterns Conflict:
1. **Stop immediately** and ask for clarification
2. **Present both patterns** with specific examples
3. **Wait for human decision** on which to follow
4. **Update documentation** with resolved pattern

---

## Feedback Integration

### When Code is Rejected:
1. **Understand the issue** - ask specific questions
2. **Identify pattern violation** - what rule was broken?
3. **Update approach** - how should it be done?
4. **Implement fix** - follow corrected pattern
5. **Confirm understanding** - restate the rule

### When Patterns Evolve:
1. **Note the change** in approach or requirements
2. **Update mental model** of correct patterns
3. **Apply consistently** in future implementations
4. **Suggest documentation updates** if needed

---

## Emergency Protocols

### If You Realize You're About to Violate Rules:
1. **STOP immediately** before making the violation
2. **Commit current safe state** if any work is salvageable
3. **Explain the situation** and what rule would be violated
4. **Ask for guidance** on correct approach
5. **Wait for explicit direction** before proceeding

### If You Discover You've Already Violated Rules:
1. **Stop all work immediately**
2. **Assess the violation** - what rule was broken?
3. **Commit current state** with clear violation message
4. **Explain what happened** and how to fix it
5. **Wait for human guidance** on remediation

---

## Success Metrics

### Good AI Collaboration:
- Code follows patterns consistently
- Minimal back-and-forth on requirements
- Fast implementation of repetitive tasks
- High code quality with few review cycles
- Clear communication about boundaries

### Poor AI Collaboration:
- Frequent pattern violations
- Scope creep beyond defined tasks
- Security or compliance violations
- Inconsistent code quality
- Unclear communication about limitations

**Remember: The goal is to accelerate development while maintaining safety and quality.**
