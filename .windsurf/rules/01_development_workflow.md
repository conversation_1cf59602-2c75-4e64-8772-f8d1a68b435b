---
trigger: always_on
---

# Development Workflow Rules

**Purpose**: Enforce controlled, task-based development with proper git practices.  
**Audience**: AI developer following todo.txt tasks.  
**Critical**: These rules prevent scope creep and ensure code quality.

---

## 🛑 CORE CONSTRAINT: ONE TASK AT A TIME

### NEVER Do Without Explicit Permission:
- Start the next todo.txt task
- Expand scope beyond current task description
- Install new dependencies
- Modify existing code outside current task
- Create database tables not specified in current task
- Add API endpoints beyond task requirements
- Merge branches to main
- Change overall architecture

### ALWAYS Do:
- Create feature branch for each task
- Commit frequently with conventional messages
- Ask permission before starting next task
- Update todo.txt status with every change
- Push branch before marking task complete

---

## Git Workflow (Mandatory)

### Branch Strategy:
```bash
# Feature branches: feat/task-description
# Fix branches: fix/issue-description  
# Chore branches: chore/maintenance-task
```

### Before Starting Any Task:
```bash
git checkout main
git pull origin main
git checkout -b feat/descriptive-task-name
```

### Commit Rules:
- **Frequency**: Commit every 30 minutes of work minimum
- **Messages**: Use conventional commits
  ```
  feat: implement canonical records schema
  fix: resolve pagination cursor encoding
  chore: update dependencies
  test: add projection service tests
  docs: update API documentation
  ```

### Task Completion Workflow:
```bash
# 1. Complete implementation
git add .
git commit -m "feat: complete [task description]"
git push origin feat/branch-name

# 2. Update todo.txt
# Change [ ] → [x] in todo.txt
git add .project/todo.txt
git commit -m "chore: mark task complete in todo.txt"
git push origin feat/branch-name

# 3. STOP and ask for permission to continue
```

---

## Communication Protocol

### Starting a Task:
**Required phrase**: "Starting task: [exact task description from todo.txt]. Creating branch feat/[branch-name]."

### Completing a Task:
**Required phrase**: "Task complete: [task description]. Code committed to feat/[branch-name]. Ready for next task or should I wait for review?"

### Need Clarification:
**Required phrase**: "Need clarification on current task: [specific question]. Should I proceed with [proposed approach] or wait for guidance?"

### Scope Expansion Discovered:
**Required phrase**: "While working on [current task], discovered [issue/opportunity]. This requires [additional work]. Should I address this now or create separate task?"

---

## Quality Gates

### Before Each Commit:
- [ ] Code follows three-noun principle (Collection, Screen, Workflow)
- [ ] No PHI in logs, events, or workflow history
- [ ] Uses keyset pagination (never OFFSET)
- [ ] Includes provenance tracking where applicable
- [ ] Follows TypeScript/ESLint rules
- [ ] Has basic error handling
- [ ] No console.log or debug code

### Before Marking Task Complete:
- [ ] Task requirements fully implemented
- [ ] Code is tested and working
- [ ] Documentation updated if needed
- [ ] Git branch is clean and pushed
- [ ] Ready for code review

---

## Emergency Stop Protocol

### Stop Immediately If:
- About to modify code outside current task scope
- Need to install dependencies not mentioned in task
- Unsure about task requirements
- Task conflicts with existing architecture
- Tempted to "improve" something not in current task

### Recovery Actions:
1. **STOP coding immediately**
2. **Commit current work**: `git commit -m "WIP: stopping for clarification"`
3. **Ask user for guidance** with specific question
4. **Wait for explicit direction** before continuing

---

## Todo.txt Integration

### Status Updates:
- **[ ] → [/]**: When starting task (after creating branch)
- **[/] → [x]**: When task complete and tested
- **[x] → [ ]**: If task needs rework (with explanation)
- **[ ] → [-]**: If task cancelled (with reason)

### Status Format:
```
[/] Set up PostgreSQL with JSONB support
    Branch: feat/postgresql-jsonb-setup
    Started: 2025-09-16 14:30
    
[x] Set up PostgreSQL with JSONB support  
    Branch: feat/postgresql-jsonb-setup
    Completed: 2025-09-16 15:45
    Commit: abc123f
```

---

## Violation Recovery

**If you catch yourself violating these rules:**

1. **STOP immediately**
2. **Commit current state**: `git commit -m "WIP: stopping for rule violation"`
3. **Explain what happened** and which rule was violated
4. **Ask for guidance** on how to proceed
5. **Wait for explicit direction** before continuing

---

## Golden Rules

1. **One task, one branch, one focus**
2. **Commit early, commit often, push regularly**
3. **Ask before expanding, never assume scope**
4. **Mark progress in todo.txt with every status change**
5. **Stop and ask when in doubt**
6. **Never merge to main without explicit permission**
7. **Always wait for user approval before starting next task**

**Remember: It's better to ask and wait than to build the wrong thing.**
