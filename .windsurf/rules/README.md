# Continuia Health OS - Development Rules

This directory contains the complete rule set for developing Continuia Health OS. Each file has a specific purpose and audience.

---

## Rule Files Overview

### 📋 [project.md](./project.md)
**Purpose**: Technical architecture specification  
**Audience**: All developers  
**Content**: Database schemas, API contracts, system architecture  
**When to read**: Before starting any development work

### 🔄 [01_development_workflow.md](./01_development_workflow.md)
**Purpose**: Git workflow and task management  
**Audience**: AI developers following todo.txt  
**Content**: Branch strategy, commit rules, task completion protocol  
**When to read**: Before starting each task

### 💻 [02_coding_standards.md](./02_coding_standards.md)
**Purpose**: Code quality and architecture patterns  
**Audience**: All developers  
**Content**: Three-noun principle, performance targets, security rules  
**When to read**: During implementation and code review

### 🤖 [03_ai_collaboration.md](./03_ai_collaboration.md)
**Purpose**: Human-AI collaboration boundaries  
**Audience**: AI agents and human reviewers  
**Content**: What AI can/cannot do, safety guardrails, communication patterns  
**When to read**: Before AI starts any autonomous work

---

## Quick Reference

### For Starting Development:
1. Read `project.md` for architecture understanding
2. Follow `01_development_workflow.md` for git practices
3. Apply `02_coding_standards.md` during implementation
4. Respect `03_ai_collaboration.md` boundaries

### Core Principles (From All Rules):
- **Three-noun mental model**: Collection, Screen, Workflow
- **PHI never leaves database**: Only IDs in events/workflows
- **One task at a time**: No scope expansion without permission
- **Keyset pagination always**: Never use OFFSET
- **Tenant isolation**: Row-level security by tenant_id
- **Blue/green deployment**: Zero-downtime evolution

### Critical Constraints:
- AI cannot start next task without permission
- No PHI in logs, events, or workflow history
- Always use generated REST APIs and hooks
- Commit frequently with conventional messages
- Ask before installing dependencies or changing architecture

---

## Rule Hierarchy

### 1. Safety Rules (Never Violate):
- PHI protection and compliance (from all files)
- Tenant isolation and security (from project.md, 02_coding_standards.md)
- Scope control and permission (from 01_development_workflow.md, 03_ai_collaboration.md)

### 2. Architecture Rules (Follow Strictly):
- Three-noun principle (from project.md, 02_coding_standards.md)
- Database patterns and API contracts (from project.md)
- Performance targets (from 02_coding_standards.md)

### 3. Process Rules (Follow Consistently):
- Git workflow and branching (from 01_development_workflow.md)
- Task management and communication (from 01_development_workflow.md, 03_ai_collaboration.md)
- Code quality standards (from 02_coding_standards.md)

---

## Emergency Procedures

### If Rules Conflict:
1. **Safety rules always win** (PHI, security, compliance)
2. **Stop and ask human** for clarification
3. **Document the conflict** for future rule updates

### If Unsure About Rules:
1. **Stop current work**
2. **Ask specific question** about the rule interpretation
3. **Wait for clarification** before proceeding

### If Rules Are Violated:
1. **Stop immediately**
2. **Commit current state** with violation message
3. **Explain what happened** and which rule was broken
4. **Ask for guidance** on remediation
5. **Wait for explicit direction**

---

## Rule Maintenance

### When to Update Rules:
- New architecture decisions are made
- Patterns evolve or change
- Security requirements change
- Compliance requirements change
- Development process improvements

### How to Update Rules:
1. **Human architect** makes the decision
2. **Update relevant rule file(s)**
3. **Communicate changes** to all developers
4. **Update this README** if file purposes change

### Rule Validation:
- All code must pass rule compliance checks
- Regular rule review sessions
- Update rules based on lessons learned
- Keep rules current with project evolution

---

## Success Indicators

### Good Rule Following:
- Consistent code patterns across the project
- No security or compliance violations
- Smooth git workflow with clear history
- Efficient human-AI collaboration
- Fast development with high quality

### Poor Rule Following:
- Inconsistent code patterns
- Security or compliance issues
- Messy git history or workflow problems
- Frequent scope creep or permission violations
- Slow development or quality issues

---

## Getting Started Checklist

Before writing any code:
- [ ] Read and understand `project.md` architecture
- [ ] Set up git workflow per `01_development_workflow.md`
- [ ] Review coding standards in `02_coding_standards.md`
- [ ] Understand AI collaboration rules in `03_ai_collaboration.md`
- [ ] Identify current task in `.project/todo.txt`
- [ ] Create feature branch for the task
- [ ] Mark task as in-progress `[/]`

**Remember: These rules exist to ensure we build a secure, compliant, and maintainable healthcare system. When in doubt, ask for clarification rather than guessing.**
