# Continuia Health OS - Git Ignore Rules

# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variable files
.env
.env.development.local
.env.test.local
.env.production.local
.env.local

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# Build outputs
build/
dist/
lib/
*.tgz
*.tar.gz

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log

# Database
*.sqlite
*.sqlite3
*.db

# Temporary files
tmp/
temp/
.tmp/

# Docker
.dockerignore

# Kubernetes
*.yaml.bak
*.yml.bak

# Terraform
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl

# Secrets and sensitive files
*.pem
*.key
*.crt
*.p12
*.pfx
secrets/
.secrets/

# Local development
.local/
local/

# Test artifacts
test-results/
playwright-report/
test-results.xml

# Storybook build outputs
storybook-static/

# Temporal
.temporal/

# PostgreSQL
*.dump
*.sql.gz

# NATS
nats-server.log

# Health OS specific
.continuia/
*.phi
*.protected

# Backup files
*.bak
*.backup
*.old

# Package manager locks (keep yarn.lock, package-lock.json for reproducible builds)
# Uncomment if using different package managers
# package-lock.json
# yarn.lock
# pnpm-lock.yaml

# Generated files
generated/
auto-generated/
.generated/

# Documentation builds
docs/build/
site/

# Monitoring and observability
.prometheus/
.grafana/
.jaeger/

# Local configuration overrides
config.local.*
settings.local.*
.env.override
