# Continuia Health OS - Developer Todo List
# Status: [ ] = Not Started, [/] = In Progress, [x] = Done, [-] = Cancelled
# Created: 2025-09-16
# Focus: Technical implementation only - Collections, Screens, Workflows

## Week 1-2: Base Infrastructure & Schema

### Database Foundation
[ ] Set up PostgreSQL with JSONB support
[ ] Implement canonical records table (id, tenant_id, collection_id, version, body, timestamps)
[ ] Create outbox table for event publishing (id, tenant_id, collection_id, record_id, event_type, occurred_at, published)
[ ] Create provenance table (id, tenant_id, record_id, actor, action, at)
[ ] Set up row-level security by tenant_id
[ ] Configure pgaudit for compliance logging

### Event Fabric Setup
[ ] Configure NATS JetStream server
[ ] Implement outbox relayer service
[ ] Set up event publishing for data.(collection).(created|updated|deleted)
[ ] Create projection service consumer framework
[ ] Implement WebSocket bridge for live UI updates

### Temporal Workflow Engine
[ ] Set up Temporal server and workers
[ ] Create wf_binding table (tenant_id, collection_slug, record_id, wf_type, wf_id, run_id, status, updated_at)
[ ] Implement REST facade for workflows (/wf/start, /wf/signal, /wf/query, /wf/status)
[ ] Create base workflow templates (EncounterWorkflow, TaskWorkflow)
[ ] Ensure PHI never enters workflow inputs/history - IDs only

## Week 3: Data APIs & Schema Studio

### Core Data APIs
[ ] Implement POST /data/:collection (create records)
[ ] Implement PUT /data/:collection/:id (update records)
[ ] Implement GET /data/:collection/:id (read single record)
[ ] Implement GET /view/:projectionId with keyset pagination
[ ] Implement POST /search with full-text capabilities
[ ] Add automatic provenance stamping for all changes

### Schema Management
[ ] Create collections metadata table
[ ] Implement collection schema validation with Zod
[ ] Build schema studio UI for collection management
[ ] Add collection versioning and migration support
[ ] Implement projection schema generation from collections

## Week 4: UI System & Screen Builder

### UI Component System (@ui package)
[ ] Set up React + TypeScript + Tailwind CSS + shadcn
[ ] Configure Storybook for component development
[ ] Create base components with Zod prop schemas
[ ] Implement data binding system (record, projection, workflow query)
[ ] Create action system (write, workflow signal, refresh)

### Screen Management System
[ ] Create ui_screen and ui_screen_version tables
[ ] Create ui_screen_pack and ui_screen_pack_item tables for blue/green deployment
[ ] Implement screen renderer (@ui-runtime) mapping JSON → React tree
[ ] Build screen builder UI for drag-and-drop layout creation
[ ] Implement pack router with traffic percentage support
[ ] Add screen export/import functionality

### Block Layout System
[ ] Create base block components (table, form, board, calendar, timeline, card grid, inbox)
[ ] Implement block event system (selection.changed, row.opened, dateRange.changed)
[ ] Create declarative link system between blocks
[ ] Implement link operations (set, mergeFilters, signal, refresh)
[ ] Add block configuration and customization UI

## Week 5: Workflow Integration & Notifications

### Workflow-UI Integration
[ ] Implement workflow signal triggers from UI actions
[ ] Create workflow query bindings for UI data
[ ] Add workflow status indicators in UI components
[ ] Implement human-in-the-loop task creation from workflows
[ ] Create deep link system with JWS tokens for notifications

### Notification System
[ ] Create notification activity for Temporal workflows
[ ] Implement in-app inbox block for actionable tasks
[ ] Create notification table and management system
[ ] Add email/SMS notification capabilities
[ ] Implement notification preferences and routing

## Week 6: Testing & Production Readiness

### Testing Framework
[ ] Set up automated testing pipeline with Jest/Vitest
[ ] Create Storybook visual regression tests
[ ] Implement API integration tests
[ ] Set up Temporal workflow testing with fake workers
[ ] Create end-to-end tests for critical user journeys
[ ] Add performance benchmarking tests

### Production Setup
[ ] Configure production PostgreSQL with replication
[ ] Set up production NATS cluster
[ ] Configure production Temporal cluster
[ ] Implement monitoring and alerting (Prometheus/Grafana)
[ ] Set up log aggregation and analysis
[ ] Configure backup and disaster recovery
[ ] Implement health checks and readiness probes

### Security & Compliance
[ ] Implement OIDC authentication integration
[ ] Set up ABAC authorization with OPA middleware
[ ] Configure mTLS for internal service communication
[ ] Add PHI masking for projections
[ ] Implement audit logging for all data access
[ ] Create compliance reporting dashboards

## Development Guidelines:
- Think only in Collections, Screens, Workflows - nothing else
- PHI never leaves database - only IDs in events/workflows
- Always use keyset pagination, never OFFSET
- All changes must have provenance tracking
- Use generated REST + hooks (useProjection, useRecord, useWorkflowSignal)
- Target: <120ms p95 reads, <250ms writes, <2s projector lag
- Maintain blue/green deployment capability for all components
