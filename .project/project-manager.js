#!/usr/bin/env node

/**
 * Continuia Project Manager
 * Simple CLI tool for managing platform development and customer solutions
 * Uses LowDB for JSON-based project management
 */

const fs = require('fs');
const path = require('path');

class ProjectManager {
  constructor() {
    this.platformFile = path.join(__dirname, 'platform_development.json');
    this.solutionsFile = path.join(__dirname, 'customer_engineering_solutions.json');
  }

  loadProject(type = 'platform') {
    const file = type === 'platform' ? this.platformFile : this.solutionsFile;
    try {
      return JSON.parse(fs.readFileSync(file, 'utf8'));
    } catch (error) {
      console.error(`Error loading ${type} project:`, error.message);
      return null;
    }
  }

  saveProject(data, type = 'platform') {
    const file = type === 'platform' ? this.platformFile : this.solutionsFile;
    try {
      data.metadata.lastUpdated = new Date().toISOString().split('T')[0];
      fs.writeFileSync(file, JSON.stringify(data, null, 2));
      console.log(`✅ ${type} project saved successfully`);
      return true;
    } catch (error) {
      console.error(`<PERSON>rror saving ${type} project:`, error.message);
      return false;
    }
  }

  updateTaskStatus(taskId, newStatus, type = 'platform') {
    const project = this.loadProject(type);
    if (!project) return false;

    let taskFound = false;
    
    // Find and update the task
    for (const phase of project.phases) {
      for (const category of phase.categories) {
        for (const task of category.tasks) {
          if (task.id === taskId) {
            const oldStatus = task.status;
            task.status = newStatus;
            task.updated = new Date().toISOString().split('T')[0];
            
            console.log(`📝 Task ${taskId}: ${oldStatus} → ${newStatus}`);
            console.log(`   "${task.title}"`);
            
            taskFound = true;
            break;
          }
        }
        if (taskFound) break;
      }
      if (taskFound) break;
    }

    if (!taskFound) {
      console.error(`❌ Task ${taskId} not found`);
      return false;
    }

    // Update metadata
    this.updateMetadata(project);
    return this.saveProject(project, type);
  }

  updateMetadata(project) {
    let totalTasks = 0;
    let completedTasks = 0;
    let inProgressTasks = 0;
    let notStartedTasks = 0;
    let totalEstimatedHours = 0;
    let totalActualHours = 0;

    for (const phase of project.phases) {
      for (const category of phase.categories) {
        for (const task of category.tasks) {
          totalTasks++;
          totalEstimatedHours += task.estimatedHours || 0;
          totalActualHours += task.actualHours || 0;

          switch (task.status) {
            case 'completed':
              completedTasks++;
              break;
            case 'in_progress':
              inProgressTasks++;
              break;
            case 'not_started':
              notStartedTasks++;
              break;
          }
        }
      }
    }

    project.metadata = {
      ...project.metadata,
      totalTasks,
      completedTasks,
      inProgressTasks,
      notStartedTasks,
      totalEstimatedHours,
      totalActualHours,
      overallProgress: totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0
    };
  }

  showStatus(type = 'both') {
    const types = type === 'both' ? ['platform', 'solutions'] : [type];
    
    for (const projectType of types) {
      const project = this.loadProject(projectType);
      if (!project) continue;

      console.log(`\n🚀 ${project.project.name}`);
      console.log(`📊 Progress: ${project.metadata.overallProgress}% (${project.metadata.completedTasks}/${project.metadata.totalTasks} tasks)`);
      console.log(`⏱️  Hours: ${project.metadata.totalActualHours}/${project.metadata.totalEstimatedHours} estimated`);
      
      // Show current tasks by status
      const statusCounts = {
        not_started: project.metadata.notStartedTasks,
        in_progress: project.metadata.inProgressTasks,
        completed: project.metadata.completedTasks
      };

      console.log(`📋 Status: ${statusCounts.not_started} pending, ${statusCounts.in_progress} active, ${statusCounts.completed} done`);
    }
  }

  listTasks(type = 'platform', status = null) {
    const project = this.loadProject(type);
    if (!project) return;

    console.log(`\n📋 ${project.project.name} - Tasks`);
    
    for (const phase of project.phases) {
      console.log(`\n📁 ${phase.name}`);
      
      for (const category of phase.categories) {
        console.log(`  📂 ${category.name}`);
        
        for (const task of category.tasks) {
          if (status && task.status !== status) continue;
          
          const statusIcon = {
            not_started: '⭕',
            in_progress: '🔄', 
            completed: '✅',
            cancelled: '❌',
            blocked: '🚫'
          }[task.status] || '❓';
          
          const priority = task.priority === 'critical' ? '🔥' : 
                          task.priority === 'high' ? '⚡' : 
                          task.priority === 'medium' ? '📋' : '📝';
          
          console.log(`    ${statusIcon} ${priority} ${task.id}: ${task.title}`);
          if (task.dependencies.length > 0) {
            console.log(`      🔗 Depends on: ${task.dependencies.join(', ')}`);
          }
        }
      }
    }
  }

  startTask(taskId, type = 'platform') {
    return this.updateTaskStatus(taskId, 'in_progress', type);
  }

  completeTask(taskId, type = 'platform') {
    return this.updateTaskStatus(taskId, 'completed', type);
  }

  // CLI interface
  run() {
    const args = process.argv.slice(2);
    const command = args[0];
    
    switch (command) {
      case 'status':
        this.showStatus(args[1] || 'both');
        break;
        
      case 'list':
        this.listTasks(args[1] || 'platform', args[2]);
        break;
        
      case 'start':
        if (!args[1]) {
          console.error('❌ Usage: start <task-id> [platform|solutions]');
          return;
        }
        this.startTask(args[1], args[2] || 'platform');
        break;
        
      case 'complete':
        if (!args[1]) {
          console.error('❌ Usage: complete <task-id> [platform|solutions]');
          return;
        }
        this.completeTask(args[1], args[2] || 'platform');
        break;
        
      case 'update':
        if (!args[1] || !args[2]) {
          console.error('❌ Usage: update <task-id> <status> [platform|solutions]');
          return;
        }
        this.updateTaskStatus(args[1], args[2], args[3] || 'platform');
        break;
        
      default:
        console.log(`
🚀 Continuia Project Manager

Usage:
  node project-manager.js <command> [options]

Commands:
  status [platform|solutions|both]     Show project status
  list [platform|solutions] [status]   List tasks (optionally filter by status)
  start <task-id> [type]               Mark task as in progress
  complete <task-id> [type]            Mark task as completed
  update <task-id> <status> [type]     Update task status

Examples:
  node project-manager.js status
  node project-manager.js list platform not_started
  node project-manager.js start task-001
  node project-manager.js complete sol-001 solutions
        `);
    }
  }
}

// Run if called directly
if (require.main === module) {
  const pm = new ProjectManager();
  pm.run();
}

module.exports = ProjectManager;
