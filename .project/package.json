{"name": "continuia-project-manager", "version": "1.0.0", "description": "Project management system for Continuia Health OS development", "main": "project-manager.js", "scripts": {"status": "node project-manager.js status", "list": "node project-manager.js list", "list-platform": "node project-manager.js list platform", "list-solutions": "node project-manager.js list solutions", "dev": "node -e \"console.log('🚀 Continuia Project Manager Ready'); require('./project-manager.js')\""}, "keywords": ["project-management", "healthcare", "continuia", "lowdb", "json"], "author": "Continuia Health OS Team", "license": "MIT", "dependencies": {"lowdb": "^7.0.1", "chalk": "^5.3.0", "inquirer": "^9.2.12"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=16.0.0"}}