{"project": {"name": "CRMC EHR Solutions - Customer Engineering", "description": "Building EHR solutions using the Continuia platform - Collections, Screens, Workflows", "audience": "Customer engineering team (no-code solution builders)", "client": "Colorado River Medical Center (CRMC)", "created": "2025-09-16", "updated": "2025-09-16", "version": "1.0", "type": "customer_solutions"}, "phases": [{"id": "phase-1", "name": "Clinical Collections & Workflows", "description": "Core clinical data structures and automated processes", "startDate": null, "endDate": null, "status": "not_started", "progress": 0, "categories": [{"id": "cat-patient-collections", "name": "Patient Data Collections", "description": "Define patient-related data structures using platform", "tasks": [{"id": "sol-001", "title": "Create Patient Demographics Collection", "description": "Configure patient collection with demographics, identifiers, insurance", "status": "not_started", "priority": "critical", "estimatedHours": 8, "actualHours": 0, "assignee": null, "dependencies": [], "platformFeatures": ["collections", "validation", "phi_protection"], "tags": ["patient", "demographics", "core"], "created": "2025-09-16", "updated": "2025-09-16"}, {"id": "sol-002", "title": "Create Encounter Collection", "description": "Configure encounter/visit collection with admission, discharge, transfers", "status": "not_started", "priority": "critical", "estimatedHours": 6, "actualHours": 0, "assignee": null, "dependencies": ["sol-001"], "platformFeatures": ["collections", "workflows", "notifications"], "tags": ["encounter", "adt", "core"], "created": "2025-09-16", "updated": "2025-09-16"}, {"id": "sol-003", "title": "Create Clinical Orders Collection", "description": "Configure orders collection for medications, labs, imaging, procedures", "status": "not_started", "priority": "high", "estimatedHours": 10, "actualHours": 0, "assignee": null, "dependencies": ["sol-002"], "platformFeatures": ["collections", "workflows", "decision_support"], "tags": ["orders", "cpoe", "clinical"], "created": "2025-09-16", "updated": "2025-09-16"}]}, {"id": "cat-clinical-screens", "name": "Clinical User Interfaces", "description": "Build clinical screens using platform UI components", "tasks": [{"id": "sol-004", "title": "Build Patient Chart Screen", "description": "Create comprehensive patient chart view with timeline, notes, results", "status": "not_started", "priority": "critical", "estimatedHours": 12, "actualHours": 0, "assignee": null, "dependencies": ["sol-001", "sol-002"], "platformFeatures": ["screens", "blocks", "data_binding"], "tags": ["chart", "ui", "clinical"], "created": "2025-09-16", "updated": "2025-09-16"}, {"id": "sol-005", "title": "Build Physician Order Entry Screen", "description": "Create CPOE interface with order sets, decision support, safety alerts", "status": "not_started", "priority": "high", "estimatedHours": 15, "actualHours": 0, "assignee": null, "dependencies": ["sol-003"], "platformFeatures": ["screens", "workflows", "decision_support"], "tags": ["cpoe", "orders", "ui"], "created": "2025-09-16", "updated": "2025-09-16"}, {"id": "sol-006", "title": "Build Nursing Worklist Screen", "description": "Create nursing task list with medication administration, assessments", "status": "not_started", "priority": "high", "estimatedHours": 10, "actualHours": 0, "assignee": null, "dependencies": ["sol-003"], "platformFeatures": ["screens", "workflows", "notifications"], "tags": ["nursing", "worklist", "ui"], "created": "2025-09-16", "updated": "2025-09-16"}]}, {"id": "cat-clinical-workflows", "name": "Clinical Process Workflows", "description": "Configure automated clinical processes using platform workflows", "tasks": [{"id": "sol-007", "title": "Configure Admission Workflow", "description": "Automate patient admission process with notifications and tasks", "status": "not_started", "priority": "high", "estimatedHours": 8, "actualHours": 0, "assignee": null, "dependencies": ["sol-002"], "platformFeatures": ["workflows", "notifications", "task_management"], "tags": ["admission", "workflow", "automation"], "created": "2025-09-16", "updated": "2025-09-16"}, {"id": "sol-008", "title": "Configure Medication Administration Workflow", "description": "Automate medication verification, administration, and documentation", "status": "not_started", "priority": "critical", "estimatedHours": 12, "actualHours": 0, "assignee": null, "dependencies": ["sol-003"], "platformFeatures": ["workflows", "barcode_scanning", "safety_checks"], "tags": ["medication", "workflow", "safety"], "created": "2025-09-16", "updated": "2025-09-16"}]}]}, {"id": "phase-2", "name": "Administrative Solutions", "description": "Revenue cycle, scheduling, and administrative workflows", "startDate": null, "endDate": null, "status": "not_started", "progress": 0, "categories": [{"id": "cat-revenue-cycle", "name": "Revenue Cycle Management", "description": "Configure billing, coding, and revenue cycle processes", "tasks": [{"id": "sol-009", "title": "Configure Patient Registration Collection", "description": "Set up registration data with insurance verification", "status": "not_started", "priority": "high", "estimatedHours": 6, "actualHours": 0, "assignee": null, "dependencies": ["sol-001"], "platformFeatures": ["collections", "integrations", "validation"], "tags": ["registration", "insurance", "revenue"], "created": "2025-09-16", "updated": "2025-09-16"}, {"id": "sol-010", "title": "Build Registration Screen", "description": "Create patient registration interface with insurance verification", "status": "not_started", "priority": "high", "estimatedHours": 8, "actualHours": 0, "assignee": null, "dependencies": ["sol-009"], "platformFeatures": ["screens", "integrations", "validation"], "tags": ["registration", "ui", "revenue"], "created": "2025-09-16", "updated": "2025-09-16"}]}]}], "metadata": {"totalTasks": 10, "completedTasks": 0, "inProgressTasks": 0, "notStartedTasks": 10, "totalEstimatedHours": 95, "totalActualHours": 0, "overallProgress": 0, "lastUpdated": "2025-09-16"}, "settings": {"statusOptions": ["not_started", "in_progress", "completed", "cancelled", "blocked"], "priorityOptions": ["low", "medium", "high", "critical"], "platformFeatures": ["collections", "screens", "workflows", "blocks", "data_binding", "notifications", "decision_support", "phi_protection", "validation", "integrations", "barcode_scanning", "safety_checks", "task_management"], "tagColors": {"patient": "#3B82F6", "clinical": "#10B981", "ui": "#06B6D4", "workflow": "#8B5CF6", "core": "#EF4444", "revenue": "#F59E0B", "safety": "#DC2626"}}}