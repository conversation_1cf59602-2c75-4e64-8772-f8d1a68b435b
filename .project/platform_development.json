{"project": {"name": "Continuia Health OS - Platform Development", "description": "Core platform infrastructure development - NOT EHR solutions", "audience": "Software developers coding the platform", "created": "2025-09-16", "updated": "2025-09-16", "version": "1.0", "type": "platform_development"}, "phases": [{"id": "phase-1", "name": "Week 1-2: Base Infrastructure & Schema", "description": "Foundation database, event fabric, and workflow engine setup", "startDate": null, "endDate": null, "status": "not_started", "progress": 0, "categories": [{"id": "cat-db-foundation", "name": "Database Foundation", "description": "PostgreSQL setup with JSONB, security, and compliance", "tasks": [{"id": "task-001", "title": "Set up PostgreSQL with JSONB support", "description": "Install and configure PostgreSQL with JSONB extensions", "status": "not_started", "priority": "high", "estimatedHours": 4, "actualHours": 0, "assignee": null, "branch": null, "commits": [], "dependencies": [], "tags": ["database", "setup"], "created": "2025-09-16", "updated": "2025-09-16"}, {"id": "task-002", "title": "Implement canonical records table", "description": "Create records table (id, tenant_id, collection_id, version, body, timestamps)", "status": "not_started", "priority": "high", "estimatedHours": 3, "actualHours": 0, "assignee": null, "branch": null, "commits": [], "dependencies": ["task-001"], "tags": ["database", "schema"], "created": "2025-09-16", "updated": "2025-09-16"}, {"id": "task-003", "title": "Create outbox table for event publishing", "description": "Outbox pattern table (id, tenant_id, collection_id, record_id, event_type, occurred_at, published)", "status": "not_started", "priority": "high", "estimatedHours": 2, "actualHours": 0, "assignee": null, "branch": null, "commits": [], "dependencies": ["task-002"], "tags": ["database", "events"], "created": "2025-09-16", "updated": "2025-09-16"}, {"id": "task-004", "title": "Create provenance table", "description": "Provenance tracking (id, tenant_id, record_id, actor, action, at)", "status": "not_started", "priority": "high", "estimatedHours": 2, "actualHours": 0, "assignee": null, "branch": null, "commits": [], "dependencies": ["task-002"], "tags": ["database", "compliance"], "created": "2025-09-16", "updated": "2025-09-16"}, {"id": "task-005", "title": "Set up row-level security by tenant_id", "description": "Implement RLS policies for multi-tenant isolation", "status": "not_started", "priority": "critical", "estimatedHours": 4, "actualHours": 0, "assignee": null, "branch": null, "commits": [], "dependencies": ["task-002"], "tags": ["database", "security", "compliance"], "created": "2025-09-16", "updated": "2025-09-16"}, {"id": "task-006", "title": "Configure pgaudit for compliance logging", "description": "Set up PostgreSQL audit logging for HIPAA compliance", "status": "not_started", "priority": "high", "estimatedHours": 3, "actualHours": 0, "assignee": null, "branch": null, "commits": [], "dependencies": ["task-001"], "tags": ["database", "compliance", "audit"], "created": "2025-09-16", "updated": "2025-09-16"}]}, {"id": "cat-event-fabric", "name": "Event Fabric Setup", "description": "NATS JetStream and event processing infrastructure", "tasks": [{"id": "task-007", "title": "Configure NATS JetStream server", "description": "Set up NATS JetStream for event streaming", "status": "not_started", "priority": "high", "estimatedHours": 4, "actualHours": 0, "assignee": null, "branch": null, "commits": [], "dependencies": [], "tags": ["events", "infrastructure"], "created": "2025-09-16", "updated": "2025-09-16"}, {"id": "task-008", "title": "Implement outbox relayer service", "description": "Service to publish database events to NATS", "status": "not_started", "priority": "high", "estimatedHours": 6, "actualHours": 0, "assignee": null, "branch": null, "commits": [], "dependencies": ["task-003", "task-007"], "tags": ["events", "service"], "created": "2025-09-16", "updated": "2025-09-16"}, {"id": "task-009", "title": "Set up event publishing for data operations", "description": "Publish data.(collection).(created|updated|deleted) events", "status": "not_started", "priority": "high", "estimatedHours": 4, "actualHours": 0, "assignee": null, "branch": null, "commits": [], "dependencies": ["task-008"], "tags": ["events", "api"], "created": "2025-09-16", "updated": "2025-09-16"}, {"id": "task-010", "title": "Create projection service consumer framework", "description": "Framework for consuming events and updating projections", "status": "not_started", "priority": "high", "estimatedHours": 8, "actualHours": 0, "assignee": null, "branch": null, "commits": [], "dependencies": ["task-009"], "tags": ["events", "projections"], "created": "2025-09-16", "updated": "2025-09-16"}, {"id": "task-011", "title": "Implement WebSocket bridge for live UI updates", "description": "Real-time UI updates via WebSocket from events", "status": "not_started", "priority": "medium", "estimatedHours": 6, "actualHours": 0, "assignee": null, "branch": null, "commits": [], "dependencies": ["task-009"], "tags": ["events", "ui", "websocket"], "created": "2025-09-16", "updated": "2025-09-16"}]}]}], "metadata": {"totalTasks": 11, "completedTasks": 0, "inProgressTasks": 0, "notStartedTasks": 11, "totalEstimatedHours": 46, "totalActualHours": 0, "overallProgress": 0, "lastUpdated": "2025-09-16"}, "settings": {"statusOptions": ["not_started", "in_progress", "completed", "cancelled", "blocked"], "priorityOptions": ["low", "medium", "high", "critical"], "tagColors": {"database": "#3B82F6", "events": "#10B981", "security": "#EF4444", "compliance": "#F59E0B", "api": "#8B5CF6", "ui": "#06B6D4", "infrastructure": "#6B7280"}}}