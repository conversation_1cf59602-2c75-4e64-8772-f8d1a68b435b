# Continuia Health OS - Docker Ignore Rules

# Git
.git
.gitignore
.gitattributes
.gitmodules

# Documentation
README.md
CHANGELOG.md
LICENSE
*.md
docs/
.github/

# Development files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
.npm
.yarn-integrity

# Build artifacts (exclude from base image, include in final if needed)
build/
dist/
lib/
coverage/
.nyc_output

# Test files
test/
tests/
__tests__/
*.test.js
*.test.ts
*.spec.js
*.spec.ts
test-results/
playwright-report/

# Development dependencies
.eslintrc.*
.prettierrc.*
.stylelintrc.*
jest.config.*
playwright.config.*
vitest.config.*
tsconfig.json
tsconfig.*.json

# Storybook
.storybook/
storybook-static/
*.stories.*

# Environment files (should be injected at runtime)
.env
.env.*
!.env.example

# Temporary files
tmp/
temp/
.tmp/
*.tmp

# Logs
logs/
*.log

# Database files (should be external)
*.sqlite
*.sqlite3
*.db
*.dump
*.sql.gz

# Secrets and certificates
*.pem
*.key
*.crt
*.p12
*.pfx
secrets/
.secrets/

# Local development
.local/
local/
.continuia/

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore

# Kubernetes
k8s/
kubernetes/
*.yaml
*.yml
helm/

# Terraform
*.tf
*.tfstate
*.tfstate.*
.terraform/

# CI/CD
.github/
.gitlab-ci.yml
Jenkinsfile
.circleci/

# Monitoring configs (should be external)
.prometheus/
.grafana/
.jaeger/

# Backup files
*.bak
*.backup
*.old

# IDE and editor files
.vscode/
.idea/
*.sublime-*
.atom/

# Package manager files (keep lock files for reproducible builds)
# Uncomment if you want to exclude them
# package-lock.json
# yarn.lock
# pnpm-lock.yaml

# Health OS specific exclusions
*.phi
*.protected

# Generated documentation
site/
docs/build/

# Local configuration overrides
config.local.*
settings.local.*
.env.override

# Development tools
.husky/
.lint-staged.js
.commitlintrc.*

# Cache directories
.cache/
.parcel-cache/
.next/
.nuxt/

# Runtime data that shouldn't be in image
pids/
*.pid
*.seed
*.pid.lock

# Optional directories that might contain sensitive data
uploads/
user-data/
private/
